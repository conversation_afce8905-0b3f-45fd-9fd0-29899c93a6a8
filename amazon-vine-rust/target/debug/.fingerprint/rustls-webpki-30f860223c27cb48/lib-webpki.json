{"rustc": 12610991425282158916, "features": "[\"alloc\", \"ring\", \"std\"]", "declared_features": "[\"alloc\", \"aws-lc-rs\", \"aws-lc-rs-fips\", \"default\", \"ring\", \"std\"]", "target": 5054897795206437336, "profile": 8276155916380437441, "path": 17953721246973902774, "deps": [[2883436298747778685, "pki_types", false, 8233484944717676226], [5491919304041016563, "ring", false, 9206977727539140521], [8995469080876806959, "untrusted", false, 49751416139356950]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustls-webpki-30f860223c27cb48/dep-lib-webpki", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}