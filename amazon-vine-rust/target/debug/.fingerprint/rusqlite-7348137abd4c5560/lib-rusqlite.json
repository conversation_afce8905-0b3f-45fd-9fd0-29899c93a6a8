{"rustc": 12610991425282158916, "features": "[\"bundled\", \"modern_sqlite\"]", "declared_features": "[\"array\", \"backup\", \"blob\", \"buildtime_bindgen\", \"bundled\", \"bundled-full\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"chrono\", \"collation\", \"column_decltype\", \"csv\", \"csvtab\", \"extra_check\", \"functions\", \"hooks\", \"i128_blob\", \"in_gecko\", \"limits\", \"load_extension\", \"loadable_extension\", \"modern-full\", \"modern_sqlite\", \"release_memory\", \"rusqlite-macros\", \"serde_json\", \"serialize\", \"series\", \"session\", \"sqlcipher\", \"time\", \"trace\", \"unlock_notify\", \"url\", \"uuid\", \"vtab\", \"wasm32-wasi-vfs\", \"window\", \"with-asan\"]", "target": 10662205063260755052, "profile": 8276155916380437441, "path": 14406785293980483347, "deps": [[3056352129074654578, "hashlink", false, 6046887733050616440], [3666196340704888985, "smallvec", false, 8679396854312487584], [5510864063823219921, "fallible_streaming_iterator", false, 18031172842627514635], [7896293946984509699, "bitflags", false, 9661131643021503794], [9986166984836792091, "libsqlite3_sys", false, 12896003458252719107], [12860549049674006569, "fallible_iterator", false, 4068259322885378230]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rusqlite-7348137abd4c5560/dep-lib-rusqlite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}