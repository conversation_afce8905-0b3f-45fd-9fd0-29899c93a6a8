{"rustc": 12610991425282158916, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 8276155916380437441, "path": 11248993937786550484, "deps": [[784494742817713399, "tower_service", false, 4760981806236950085], [1906322745568073236, "pin_project_lite", false, 2737154049989748897], [4121350475192885151, "iri_string", false, 11642423196100059628], [5695049318159433696, "tower", false, 8007800937716676574], [7712452662827335977, "tower_layer", false, 5595141134600199017], [7896293946984509699, "bitflags", false, 9661131643021503794], [9010263965687315507, "http", false, 8017774908463411692], [10629569228670356391, "futures_util", false, 3699458731636499038], [14084095096285906100, "http_body", false, 12246171921834844850], [16066129441945555748, "bytes", false, 17908538325133024938]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tower-http-46e830348cb7bd89/dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}