{"rustc": 12610991425282158916, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 5408242616063297496, "profile": 9568288033221340563, "path": 13906685104144274828, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustls-e52f3cc93187e2fe/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}