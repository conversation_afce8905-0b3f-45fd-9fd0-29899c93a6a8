{"rustc": 12610991425282158916, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"json\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 10900257523021023328, "path": 2780671814075230861, "deps": [[40386456601120721, "percent_encoding", false, 3917682823601096093], [784494742817713399, "tower_service", false, 4760981806236950085], [1788832197870803419, "hyper_rustls", false, 5763421085456500919], [1906322745568073236, "pin_project_lite", false, 2737154049989748897], [2054153378684941554, "tower_http", false, 6024873274581505368], [2517136641825875337, "sync_wrapper", false, 13201391731600950603], [2883436298747778685, "rustls_pki_types", false, 8233484944717676226], [3150220818285335163, "url", false, 16768788639891793044], [5695049318159433696, "tower", false, 8007800937716676574], [5907992341687085091, "webpki_roots", false, 11757907445398994316], [5986029879202738730, "log", false, 3245732284634931154], [7620660491849607393, "futures_core", false, 2559374555774831430], [9010263965687315507, "http", false, 8017774908463411692], [9689903380558560274, "serde", false, 4285433165694830255], [11895591994124935963, "tokio_rustls", false, 9701311086721995517], [11957360342995674422, "hyper", false, 2651160648233051032], [12393800526703971956, "tokio", false, 7410478292664746240], [13077212702700853852, "base64", false, 13818684285075868678], [14084095096285906100, "http_body", false, 12246171921834844850], [15367738274754116744, "serde_json", false, 8273000131773917072], [16066129441945555748, "bytes", false, 17908538325133024938], [16400140949089969347, "rustls", false, 9786697155600495633], [16542808166767769916, "serde_urlencoded", false, 14562752976073506201], [16680807377217054954, "hyper_util", false, 16670316556026767212], [16900715236047033623, "http_body_util", false, 5386252631188310109]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/reqwest-6148e5d259edae15/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}