{"rustc": 12610991425282158916, "features": "[\"http1\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 8276155916380437441, "path": 1817758552980461870, "deps": [[784494742817713399, "tower_service", false, 4760981806236950085], [2883436298747778685, "pki_types", false, 8233484944717676226], [5907992341687085091, "webpki_roots", false, 11757907445398994316], [9010263965687315507, "http", false, 8017774908463411692], [11895591994124935963, "tokio_rustls", false, 9701311086721995517], [11957360342995674422, "hyper", false, 2651160648233051032], [12393800526703971956, "tokio", false, 7410478292664746240], [16400140949089969347, "rustls", false, 9786697155600495633], [16680807377217054954, "hyper_util", false, 16670316556026767212]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hyper-rustls-b8293ad90072dbeb/dep-lib-hyper_rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}