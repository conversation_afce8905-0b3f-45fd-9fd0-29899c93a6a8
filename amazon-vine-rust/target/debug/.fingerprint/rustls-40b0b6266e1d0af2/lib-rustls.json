{"rustc": 12610991425282158916, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 15030315195695952907, "path": 16709866344541937829, "deps": [[2883436298747778685, "pki_types", false, 8233484944717676226], [3722963349756955755, "once_cell", false, 3677258689314139479], [5491919304041016563, "ring", false, 9206977727539140521], [6528079939221783635, "zeroize", false, 2190376337546069006], [16400140949089969347, "build_script_build", false, 4015953272915048002], [17003143334332120809, "subtle", false, 15916050568757630855], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 881231542045283192]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustls-40b0b6266e1d0af2/dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}