{"rustc": 12610991425282158916, "features": "[\"attributes\", \"default\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 3992724396554112236, "path": 8820282621283276246, "deps": [[325572602735163265, "tracing_attributes", false, 3554828866022501781], [1906322745568073236, "pin_project_lite", false, 2737154049989748897], [3424551429995674438, "tracing_core", false, 15218823596301942058]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-e7e226c23aeeda7a/dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}