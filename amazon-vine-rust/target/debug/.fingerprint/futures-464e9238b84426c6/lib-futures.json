{"rustc": 12610991425282158916, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 336243669335521001, "path": 10482646980954400256, "deps": [[5103565458935487, "futures_io", false, 16144439626209252576], [1811549171721445101, "futures_channel", false, 10210643669486528077], [7013762810557009322, "futures_sink", false, 14329957633631110588], [7620660491849607393, "futures_core", false, 2559374555774831430], [10629569228670356391, "futures_util", false, 3699458731636499038], [12779779637805422465, "futures_executor", false, 4063035427190765058], [16240732885093539806, "futures_task", false, 15409613491580627712]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-464e9238b84426c6/dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}