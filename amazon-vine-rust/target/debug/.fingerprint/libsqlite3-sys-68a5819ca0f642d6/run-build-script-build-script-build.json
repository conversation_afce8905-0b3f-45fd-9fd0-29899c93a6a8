{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9986166984836792091, "build_script_build", false, 5278237712902861775]], "local": [{"RerunIfChanged": {"output": "debug/build/libsqlite3-sys-68a5819ca0f642d6/output", "paths": ["sqlite3/sqlite3.c", "sqlite3/wasm32-wasi-vfs.c"]}}, {"RerunIfEnvChanged": {"var": "LIBSQLITE3_SYS_USE_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "SQLITE_MAX_VARIABLE_NUMBER", "val": null}}, {"RerunIfEnvChanged": {"var": "SQLITE_MAX_EXPR_DEPTH", "val": null}}, {"RerunIfEnvChanged": {"var": "SQLITE_MAX_COLUMN", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBSQLITE3_FLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "MACOSX_DEPLOYMENT_TARGET", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_AR", "val": null}}, {"RerunIfEnvChanged": {"var": "AR", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_aarch64-apple-darwin", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}