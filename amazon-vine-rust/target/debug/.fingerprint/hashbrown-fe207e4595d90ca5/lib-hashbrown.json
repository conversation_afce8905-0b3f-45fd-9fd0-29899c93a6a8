{"rustc": 12610991425282158916, "features": "[\"ahash\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 8276155916380437441, "path": 866464739923650988, "deps": [[966925859616469517, "ahash", false, 12146756558648899939]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-fe207e4595d90ca5/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}