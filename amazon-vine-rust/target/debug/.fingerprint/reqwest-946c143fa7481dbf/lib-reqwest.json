{"rustc": 12610991425282158916, "features": "[\"__tls\", \"default\", \"default-tls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-crate\", \"serde_json\", \"stream\", \"tokio-native-tls\", \"tokio-util\", \"wasm-streams\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 8276155916380437441, "path": 13794928639922942145, "deps": [[40386456601120721, "percent_encoding", false, 3917682823601096093], [95042085696191081, "ipnet", false, 8219151287932426209], [264090853244900308, "sync_wrapper", false, 9757837124793092889], [784494742817713399, "tower_service", false, 4760981806236950085], [1288403060204016458, "tokio_util", false, 11645034329428075825], [1906322745568073236, "pin_project_lite", false, 2737154049989748897], [3150220818285335163, "url", false, 16768788639891793044], [3722963349756955755, "once_cell", false, 3677258689314139479], [4405182208873388884, "http", false, 10275498504031317974], [5986029879202738730, "log", false, 3245732284634931154], [7414427314941361239, "hyper", false, 8889360137255426717], [7620660491849607393, "futures_core", false, 2559374555774831430], [8915503303801890683, "http_body", false, 7830992146290328755], [9689903380558560274, "serde", false, 4285433165694830255], [10229185211513642314, "mime", false, 10348454780094349134], [10629569228670356391, "futures_util", false, 3699458731636499038], [11107720164717273507, "system_configuration", false, 13850432265218247305], [12186126227181294540, "tokio_native_tls", false, 10694533597467375032], [12367227501898450486, "hyper_tls", false, 14233938518232597849], [12393800526703971956, "tokio", false, 7410478292664746240], [13809605890706463735, "h2", false, 4589730377656629058], [14564311161534545801, "encoding_rs", false, 13217713204144732263], [15367738274754116744, "serde_json", false, 8273000131773917072], [16066129441945555748, "bytes", false, 17908538325133024938], [16311359161338405624, "rustls_pemfile", false, 4932165266718671366], [16542808166767769916, "serde_urlencoded", false, 14562752976073506201], [16785601910559813697, "native_tls_crate", false, 4032059422476907365], [18066890886671768183, "base64", false, 4104302763220280075], [18071510856783138481, "mime_guess", false, 10319586401620699]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/reqwest-946c143fa7481dbf/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}