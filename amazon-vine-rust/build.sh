#!/bin/bash

echo "Building Amazon Vine Rust project..."

# Check if ChromeDriver is running
if ! pgrep -f "chromedriver" > /dev/null; then
    echo "Warning: ChromeDriver is not running. Please start it with:"
    echo "chromedriver --port=9515"
    echo ""
fi

# Build the project
cargo build --release

if [ $? -eq 0 ]; then
    echo "Build successful!"
    echo ""
    echo "To run the project:"
    echo "1. Make sure ChromeDriver is running: chromedriver --port=9515"
    echo "2. Create login.txt with your Amazon credentials"
    echo "3. Create blacklist.txt with terms to filter"
    echo "4. Run: cargo run --release"
else
    echo "Build failed!"
    exit 1
fi
