# Amazon Vine Rust

This is a Rust port of the original Kotlin Amazon Vine scraper. It automates the process of scraping Amazon Vine products and sending notifications via Telegram.

## Features

- Web scraping using Selenium WebDriver (thirtyfour crate)
- SQLite database for storing product information
- Telegram bot integration for notifications
- Blacklist filtering
- Rating extraction from Amazon product pages
- Git automation for version control

## Prerequisites

1. **ChromeDriver**: Download and install ChromeDriver from [here](https://chromedriver.chromium.org/)
2. **Chrome Browser**: Make sure Google Chrome is installed
3. **Rust**: Install Rust from [rustup.rs](https://rustup.rs/)

## Setup

1. Clone this repository
2. Create a `login.txt` file with your Amazon credentials (one per line):
   ```
   <EMAIL>
   your-password
   ```
3. Create a `blacklist.txt` file with terms to filter out (one per line)
4. Start ChromeDriver:
   ```bash
   chromedriver --port=9515
   ```
5. Build and run the project:
   ```bash
   cargo build --release
   cargo run
   ```

## Configuration

- **Telegram Bot Token**: Update the `TELEGRAM_BOT_TOKEN` constant in `src/main.rs`
- **Telegram Group IDs**: Update the group ID constants in `src/main.rs`
- **Categories**: Modify the category lists in `src/helpers.rs`

## Project Structure

- `src/main.rs` - Main application logic and scraping workflow
- `src/database.rs` - Database operations and data structures
- `src/helpers.rs` - Utility functions and category definitions
- `src/ratings.rs` - Amazon rating extraction functionality
- `src/lib.rs` - Module exports

## Dependencies

- `thirtyfour` - Selenium WebDriver bindings for Rust
- `rusqlite` - SQLite database interface
- `teloxide` - Telegram bot framework
- `tokio` - Async runtime
- `serde` - Serialization framework
- `chrono` - Date and time handling
- `reqwest` - HTTP client
- `anyhow` - Error handling

## Notes

This is a direct port from the original Kotlin version. The functionality should be equivalent, including:

- Category-based scraping
- Blacklist filtering
- Free item detection and Telegram notifications
- Database storage with duplicate prevention
- Git automation for updates

## Safety and Legal Considerations

- This tool is for educational purposes only
- Respect Amazon's terms of service and robots.txt
- Use appropriate delays between requests
- Consider rate limiting to avoid being blocked
