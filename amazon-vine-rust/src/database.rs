use anyhow::Result;
use chrono::Local;
use rusqlite::{params, Connection};
use serde::{Deserialize, Serialize};
use std::sync::Mutex;

// Global counters (equivalent to Kotlin global variables)
pub static mut INSERTED_NEW_FREE_ARTICLES: i32 = 0;
pub static mut TOTAL_NEW_BLACKLISTED_ARTICLES: i32 = 0;
pub static mut TOTAL_NEW_ARTICLES: i32 = 0;

// Thread-safe database connection
lazy_static::lazy_static! {
    static ref DB_CONNECTION: Mutex<Option<Connection>> = Mutex::new(None);
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Item {
    pub title: String,
    pub tax_price: String,
    pub link: String,
    pub description: String,
    pub category: String,
    pub sub_category: Option<String>,
    pub image_url: String,
    pub date_added: String,
    pub page_number: i32,
    pub is_blacklisted: i32,
    pub from_quick_mode: i32,
    pub rating_star: Option<String>,
    pub rating_total: Option<i32>,
}

#[derive(Debug, <PERSON><PERSON>)]
pub struct Category {
    pub root: String,
    pub sub_category: Option<String>,
}

impl Category {
    pub fn new(root: String, sub_category: Option<String>) -> Self {
        Self { root, sub_category }
    }
}

impl std::fmt::Display for Category {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match &self.sub_category {
            Some(sub) => write!(f, "{} > {}", self.root, sub),
            None => write!(f, "{}", self.root),
        }
    }
}

pub fn initialize_database() -> Result<()> {
    let conn = Connection::open("data.db")?;
    
    // Create the articles table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS articles (
            title TEXT NOT NULL,
            description TEXT NOT NULL,
            tax_price TEXT NOT NULL,
            link TEXT NOT NULL UNIQUE,
            category TEXT NOT NULL,
            sub_category TEXT,
            image_url TEXT NOT NULL,
            date_added TEXT NOT NULL,
            page_number INTEGER,
            is_blacklisted INTEGER NOT NULL DEFAULT 0,
            from_quick_mode INTEGER NOT NULL DEFAULT 0,
            rating_star TEXT,
            rating_total INTEGER
        )",
        [],
    )?;

    // Store connection in global mutex
    let mut db_conn = DB_CONNECTION.lock().unwrap();
    *db_conn = Some(conn);
    
    Ok(())
}

pub fn add_to_database(item: Item) -> Result<()> {
    if is_in_database(&item.link, item.from_quick_mode == 1)? {
        return Ok(());
    }

    let is_free = item.tax_price == "€0.00";
    if is_free {
        println!("{}: ✅ kostenlos ✅ {}", item.category, item.title);
        unsafe {
            INSERTED_NEW_FREE_ARTICLES += 1;
        }
    }

    let db_conn = DB_CONNECTION.lock().unwrap();
    if let Some(conn) = db_conn.as_ref() {
        conn.execute(
            "INSERT INTO articles (
                title, description, tax_price, link, category, sub_category,
                image_url, date_added, page_number, is_blacklisted, from_quick_mode,
                rating_star, rating_total
            ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13)",
            params![
                item.title,
                item.description,
                item.tax_price,
                item.link,
                item.category,
                item.sub_category,
                item.image_url,
                item.date_added,
                item.page_number,
                item.is_blacklisted,
                item.from_quick_mode,
                item.rating_star,
                item.rating_total,
            ],
        )?;
    }

    Ok(())
}

pub fn is_in_database(link: &str, from_quick_mode: bool) -> Result<bool> {
    let db_conn = DB_CONNECTION.lock().unwrap();
    if let Some(conn) = db_conn.as_ref() {
        let query = if from_quick_mode {
            "SELECT COUNT(*) FROM articles WHERE link = ?1 AND from_quick_mode != 1"
        } else {
            "SELECT COUNT(*) FROM articles WHERE link = ?1"
        };

        let mut stmt = conn.prepare(query)?;
        let count: i32 = stmt.query_row(params![link], |row| row.get(0))?;
        Ok(count > 0)
    } else {
        Ok(false)
    }
}

pub fn get_current_date_time() -> String {
    Local::now().format("%Y-%m-%d %H:%M:%S").to_string()
}
