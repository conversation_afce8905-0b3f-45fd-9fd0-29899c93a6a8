use anyhow::Result;
use regex::Regex;
use std::sync::Mutex;
use thirtyfour::prelude::*;

#[derive(Debug, Clone)]
pub struct Rating {
    pub image_url: String,
    pub stars: Option<String>,
    pub total: Option<i32>,
    pub price: Option<String>,
}

impl Rating {
    pub fn new() -> Self {
        Self {
            image_url: String::new(),
            stars: None,
            total: None,
            price: None,
        }
    }
}

// Global WebDriver for rating downloads (equivalent to amazonRatingDriver in Kotlin)
lazy_static::lazy_static! {
    static ref AMAZON_RATING_DRIVER: Mutex<Option<WebDriver>> = Mutex::new(None);
}

pub async fn initialize_rating_driver() -> Result<()> {
    let caps = DesiredCapabilities::chrome();
    let driver = WebDriver::new("http://localhost:9515", caps).await?;
    
    let mut rating_driver = AMAZON_RATING_DRIVER.lock().unwrap();
    *rating_driver = Some(driver);
    
    Ok(())
}

pub async fn quit_rating_driver() -> Result<()> {
    let mut rating_driver = AMAZON_RATING_DRIVER.lock().unwrap();
    if let Some(driver) = rating_driver.take() {
        driver.quit().await?;
    }
    Ok(())
}

pub async fn download_amazon_rating(url: &str) -> Result<Rating> {
    // Initialize driver if not already done
    {
        let rating_driver = AMAZON_RATING_DRIVER.lock().unwrap();
        if rating_driver.is_none() {
            drop(rating_driver);
            initialize_rating_driver().await?;
        }
    }

    let rating_driver = AMAZON_RATING_DRIVER.lock().unwrap();
    if let Some(driver) = rating_driver.as_ref() {
        driver.goto(url).await?;

        // Get total reviews
        let total = match driver.find(By::Id("acrCustomerReviewText")).await {
            Ok(element) => {
                let total_text = element.text().await?;
                let regex = Regex::new(r"^(\d+)")?;
                if let Some(captures) = regex.captures(&total_text) {
                    captures.get(1)
                        .and_then(|m| m.as_str().parse::<i32>().ok())
                } else {
                    None
                }
            }
            Err(_) => None,
        };

        // Get price
        let price = match driver.find(By::ClassName("priceToPay")).await {
            Ok(element) => {
                let price_text = element.text().await?;
                Some(price_text.replace("\n", ","))
            }
            Err(_) => None,
        };

        // Get stars rating
        let stars = match driver.find(By::Id("acrPopover")).await {
            Ok(element) => {
                let stars_text = element.text().await?;
                Some(stars_text.replace(",", "."))
            }
            Err(_) => None,
        };

        // Get image URL
        let image_url = match driver.find(By::Id("imgTagWrapperId")).await {
            Ok(wrapper) => {
                match wrapper.find(By::Tag("img")).await {
                    Ok(img) => img.attr("src").await?.unwrap_or_default(),
                    Err(_) => String::new(),
                }
            }
            Err(_) => String::new(),
        };

        Ok(Rating {
            image_url,
            stars,
            total,
            price,
        })
    } else {
        Err(anyhow::anyhow!("Rating driver not initialized"))
    }
}
