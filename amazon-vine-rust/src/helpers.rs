use anyhow::Result;
use regex::Regex;
use std::fs;
use std::time::Duration;
use thirtyfour::prelude::*;
use tokio::time::sleep;

use crate::database::Category;

#[derive(Debug, <PERSON>lone)]
pub struct CategoryUrl {
    pub url: String,
    pub name: String,
    pub info: String,
    pub total_articles: i32,
}

impl CategoryUrl {
    pub fn new(url: &str, name: &str, info: &str, total_articles: i32) -> Self {
        Self {
            url: url.to_string(),
            name: name.to_string(),
            info: info.to_string(),
            total_articles,
        }
    }
}

pub fn get_categories_in_order() -> Vec<CategoryUrl> {
    vec![
        CategoryUrl::new("&pn=355007011", "Baby", "17%", 400),
        CategoryUrl::new("&pn=12950651", "Spielzeug", "3%", 2700),
        CategoryUrl::new("&pn=340846031", "Lebensmittel & Getränke", "37", 100),
        CategoryUrl::new("&pn=64187031", "Drogerie & Körperpflege", "36", 1000),
    ]
}

pub fn get_categories_to_shuffle() -> Vec<CategoryUrl> {
    vec![
        CategoryUrl::new("&pn=84230031", "Kosmetik", "60%", 800),
        CategoryUrl::new("&pn=11961464031", "Fashion", "4", 4000),
        CategoryUrl::new("&pn=908823031", "Elektro-Großgeräte", "3", 70),
        CategoryUrl::new("&pn=340849031", "Musikinstrumente & DJ-Equipment", "2", 80),
        CategoryUrl::new("&pn=16435051", "Sport & Freizeit", "2", 1500),
        CategoryUrl::new("&pn=10925031", "Garten", "1", 600),
        CategoryUrl::new("&pn=5866098031", "Gewerbe, Industrie & Wissenschaft", "1", 300),
        CategoryUrl::new("&pn=300992", "Games", "???", 70),
        CategoryUrl::new("&pn=213083031", "Beleuchtung", "???", 400),
        CategoryUrl::new("&pn=72921031", "Sonstiges", "???", 400),
        CategoryUrl::new("&pn=78191031", "Auto & Motorrad", "???", 1100),
        CategoryUrl::new("&pn=908823031", "Elektro-Großgeräte ", "???", 60),
        CategoryUrl::new("&pn=192416031", "Bürobedarf & Schreibwaren", "1%", 700),
    ]
}

pub fn read_blacklist() -> Result<Vec<String>> {
    let content = fs::read_to_string("blacklist.txt")?;
    Ok(content
        .lines()
        .map(|line| line.trim().to_lowercase())
        .filter(|line| !line.is_empty())
        .collect())
}

pub async fn login(driver: &WebDriver, login_credentials: &[String]) -> Result<()> {
    // Try to find login elements, ignore if not found (already logged in)
    if let Ok(email_field) = driver.find(By::Id("ap_email")).await {
        email_field.send_keys(&login_credentials[0]).await?;
        
        if let Ok(password_field) = driver.find(By::Id("ap_password")).await {
            password_field.send_keys(&login_credentials[1]).await?;
        }
        
        if let Ok(checkbox) = driver.find(By::Css(".a-label.a-checkbox-label")).await {
            checkbox.click().await?;
        }
        
        if let Ok(submit_btn) = driver.find(By::Id("signInSubmit")).await {
            submit_btn.click().await?;
        }
    }
    
    sleep(Duration::from_secs(5)).await;
    Ok(())
}

pub async fn get_count_parts(driver: &WebDriver, page_number: i32, category: Option<&Category>) -> Result<Vec<i32>> {
    let container = driver.find(By::Id("vvp-items-grid-container")).await?;
    let p_element = container.find(By::Tag("p")).await?;
    let raw_text = p_element.text().await?;
    
    // Remove dots from German number formatting
    let text = raw_text.replace(".", "");
    
    let regex = Regex::new(r"(\d+)")?;
    let numbers: Vec<i32> = regex
        .find_iter(&text)
        .map(|m| m.as_str().parse::<i32>().unwrap_or(0))
        .collect();
    
    let display_text = match category {
        Some(cat) => format!("{}: Finished page {}        {}", cat, page_number, raw_text),
        None => format!("Alle anzeigen: Finished page {}        {}", page_number, raw_text),
    };
    println!("{}", display_text);
    
    Ok(numbers)
}

pub async fn is_on_last_page(driver: &WebDriver, page_number: i32, category: Option<&Category>) -> Result<bool> {
    match get_count_parts(driver, page_number, category).await {
        Ok(parts) => {
            if parts.len() >= 3 {
                Ok(parts[1] == parts[2])
            } else {
                Ok(false)
            }
        }
        Err(_) => {
            println!("Reload page because of error getting count parts");
            let current_url = driver.current_url().await?;
            driver.goto(current_url.as_str()).await?;
            Ok(false)
        }
    }
}

pub async fn retry_with_functions_ignoring_exceptions<F, Fut>(functions: Vec<F>) -> ()
where
    F: Fn() -> Fut,
    Fut: std::future::Future<Output = Result<()>>,
{
    for function in functions {
        match function().await {
            Ok(_) => break,
            Err(_) => {
                sleep(Duration::from_millis(400)).await;
                continue;
            }
        }
    }
}

// Helper function to create boxed async closures
pub type BoxedAsyncFn = Box<dyn Fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<()>> + Send>> + Send + Sync>;

pub async fn retry_with_boxed_functions(functions: Vec<BoxedAsyncFn>) -> () {
    for function in functions {
        match function().await {
            Ok(_) => break,
            Err(_) => {
                sleep(Duration::from_millis(400)).await;
                continue;
            }
        }
    }
}

pub fn read_login_credentials() -> Result<Vec<String>> {
    let content = fs::read_to_string("login.txt")?;
    Ok(content
        .lines()
        .map(|line| line.trim().to_string())
        .filter(|line| !line.is_empty())
        .collect())
}
