use anyhow::Result;
use rand::seq::SliceRandom;
use std::time::{Duration, Instant};
use teloxide::{prelude::*, types::InputFile};
use thirtyfour::prelude::*;
use tokio::time::sleep;

mod database;
mod helpers;
mod ratings;

use database::*;
use helpers::*;
use ratings::*;

// Telegram group IDs
const TELEGRAM_GROUP_KOSTENPFLICHTIG: i64 = -1002173492657;
const TELEGRAM_GROUP_KOSTENLOS: i64 = -4789277670;
const TELEGRAM_GROUP_TEUER: i64 = -4782393964;
const TELEGRAM_GROUP_BABY_SPIELZEUG_LEBENSMITTEL_DROGERIE: i64 = -4778594099;

// Telegram bot token (should be moved to environment variable in production)
const TELEGRAM_BOT_TOKEN: &str = "**********************************************";

#[tokio::main]
async fn main() -> Result<()> {
    env_logger::init();

    let start_time = Instant::now();

    // Initialize database
    initialize_database()?;

    // Read login credentials
    let login_credentials = read_login_credentials()?;

    // Setup Chrome driver
    let mut caps = DesiredCapabilities::chrome();
    caps.add_arg("--user-data-dir=selenium")?;

    let driver = WebDriver::new("http://localhost:9515", caps).await?;

    // Initialize rating driver
    initialize_rating_driver().await?;

    // Prepare categories
    let mut merged_categories = get_categories_in_order();
    let mut categories_to_shuffle = get_categories_to_shuffle();
    categories_to_shuffle.shuffle(&mut rand::thread_rng());
    merged_categories.extend(categories_to_shuffle);

    let result = run_scraping(&driver, &login_credentials, &merged_categories).await;

    // Cleanup
    quit_rating_driver().await?;
    driver.quit().await?;

    match result {
        Ok(_) => {
            let elapsed = start_time.elapsed();
            let date_time = get_current_date_time();

            unsafe {
                let inserted = INSERTED_NEW_FREE_ARTICLES;
                let blacklisted = TOTAL_NEW_BLACKLISTED_ARTICLES;
                let total = TOTAL_NEW_ARTICLES;

                println!(
                    "\nAll {} categories processed in ~{} minutes. \
                     Inserted {} new free article(s). \
                     Blacklisted {} of {} new. \
                     Finished on {}.",
                    merged_categories.len(),
                    elapsed.as_secs() / 60,
                    inserted,
                    blacklisted,
                    total,
                    date_time
                );
            }
        }
        Err(e) => {
            println!("\n\nException:\n\n{}", e);
        }
    }

    // Git operations (equivalent to the Kotlin version)
    println!("\nPushing to repository...\n\n--> git add .");
    run_git_commands().await?;

    Ok(())
}

async fn run_scraping(
    driver: &WebDriver,
    login_credentials: &[String],
    categories: &[CategoryUrl],
) -> Result<()> {
    driver.goto("https://www.amazon.de/vine/vine-items?queue=encore").await?;
    login(driver, login_credentials).await?;

    for (index, category_url) in categories.iter().enumerate() {
        let url = format!("https://www.amazon.de/vine/vine-items?queue=encore{}", category_url.url);
        driver.goto(&url).await?;
        sleep(Duration::from_secs(2)).await;

        let category = Category::new(category_url.name.clone(), None);
        check_all_articles_on_page(driver, index, categories.len(), &category).await?;
    }

    Ok(())
}

async fn check_all_articles_on_page(
    driver: &WebDriver,
    index: usize,
    category_count: usize,
    category: &Category,
) -> Result<()> {
    println!("\n{}/{} {}", index + 1, category_count, category);
    let mut page_number = 0;

    loop {
        page_number += 1;
        let articles = driver.find_all(By::ClassName("vvp-item-tile")).await?;
        let blacklist = read_blacklist()?;
        let mut inserted_anything_in_database = false;

        for article in articles {
            // Get overview link
            let overview_link = match article.find(By::ClassName("a-link-normal")).await {
                Ok(link_element) => {
                    match link_element.attr("href").await? {
                        Some(href) => format!("https://www.amazon.de{}", href),
                        None => continue,
                    }
                }
                Err(_) => {
                    println!("\n❌ .a-link-normal error, skipping article\n");
                    continue;
                }
            };

            if is_in_database(&overview_link, false)? {
                continue;
            }

            unsafe {
                TOTAL_NEW_ARTICLES += 1;
            }

            let overview_title = article
                .find(By::ClassName("vvp-item-product-title-container"))
                .await?
                .text()
                .await?
                .trim()
                .to_string();

            // Check if blacklisted
            if blacklist.iter().any(|blacklist_entry| {
                overview_title.to_lowercase().contains(blacklist_entry)
            }) {
                inserted_anything_in_database = true;
                add_blacklisted(&article, &overview_title, &overview_link, page_number, category).await?;
                unsafe {
                    TOTAL_NEW_BLACKLISTED_ARTICLES += 1;
                }
                continue;
            }

            // Click to open modal
            retry_click_modal(&driver, &article).await?;
            sleep(Duration::from_secs(2)).await;

            let title_element = driver.find(By::Id("vvp-product-details-modal--product-title")).await?;
            let title_text = title_element.text().await?;

            // Check blacklist again with full title
            if blacklist.iter().any(|blacklist_entry| {
                title_text.to_lowercase().contains(blacklist_entry)
            }) {
                inserted_anything_in_database = true;
                add_blacklisted(&article, &title_text, &overview_link, page_number, category).await?;
                continue;
            }

            let tax_price = driver
                .find(By::Id("vvp-product-details-modal--tax-value-string"))
                .await?
                .text()
                .await?;

            let image_url = driver
                .find(By::Id("vvp-product-details-modal--hero-image"))
                .await?
                .attr("src")
                .await?
                .unwrap_or_default();

            let description = driver
                .find(By::Id("vvp-product-description-expander"))
                .await?
                .text()
                .await?;

            let mut rating = Rating::new();
            let is_free = tax_price == "€0.00";

            if is_free {
                rating = download_amazon_rating(&overview_link).await?;
                send_telegram_notification(&rating, &overview_title, &tax_price, category, &overview_link, is_free).await?;
            }

            let item = Item {
                title: title_text,
                tax_price,
                link: overview_link,
                description,
                category: category.root.clone(),
                sub_category: category.sub_category.clone(),
                image_url,
                date_added: get_current_date_time(),
                page_number,
                from_quick_mode: 0,
                is_blacklisted: 0,
                rating_star: rating.stars,
                rating_total: rating.total,
            };

            add_to_database(item)?;
            inserted_anything_in_database = true;

            // Close modal
            retry_close_modal(&driver).await?;
            sleep(Duration::from_secs(2)).await;
        }

        if !inserted_anything_in_database || is_on_last_page(driver, page_number, Some(category)).await? {
            if !inserted_anything_in_database {
                println!("{}: No new articles found on page {}", category, page_number);
                sleep(Duration::from_secs(2)).await;
            }
            return Ok(());
        }

        // Go to next page
        match driver.find(By::ClassName("a-last")).await {
            Ok(next_btn) => next_btn.click().await?,
            Err(_) => {
                let current_url = driver.current_url().await?;
                driver.goto(current_url.as_str()).await?;
            }
        }
        sleep(Duration::from_secs(2)).await;
    }
}

async fn add_blacklisted(
    base: &WebElement,
    title: &str,
    link: &str,
    page_number: i32,
    category: &Category,
) -> Result<()> {
    let image_url = match base.find(By::Tag("img")).await {
        Ok(img) => img.attr("src").await?.unwrap_or_default(),
        Err(_) => String::new(),
    };

    let item = Item {
        title: title.to_string(),
        tax_price: String::new(),
        link: link.to_string(),
        description: String::new(),
        category: category.root.clone(),
        sub_category: category.sub_category.clone(),
        image_url,
        date_added: get_current_date_time(),
        page_number,
        from_quick_mode: 1,
        is_blacklisted: 1,
        rating_star: None,
        rating_total: None,
    };

    add_to_database(item)?;
    Ok(())
}

async fn retry_click_modal(driver: &WebDriver, article: &WebElement) -> Result<()> {
    // Try each method in sequence until one succeeds
    if let Ok(btn) = article.find(By::ClassName("a-button-primary")).await {
        if btn.click().await.is_ok() {
            return Ok(());
        }
    }
    sleep(Duration::from_millis(400)).await;

    if driver.execute("document.querySelector('.a-modal-scroller.a-declarative').click()", vec![]).await.is_ok() {
        return Ok(());
    }
    sleep(Duration::from_millis(400)).await;

    if let Ok(modal) = driver.find(By::Css(".a-modal-scroller.a-declarative")).await {
        if modal.click().await.is_ok() {
            return Ok(());
        }
    }
    sleep(Duration::from_millis(400)).await;

    if let Ok(back_btn) = driver.find(By::Id("vvp-product-details-modal--back-btn")).await {
        back_btn.click().await?;
    }

    Ok(())
}

async fn retry_close_modal(driver: &WebDriver) -> Result<()> {
    // Try each method in sequence until one succeeds
    if let Ok(back_btn) = driver.find(By::Id("vvp-product-details-modal--back-btn")).await {
        if back_btn.click().await.is_ok() {
            return Ok(());
        }
    }
    sleep(Duration::from_millis(400)).await;

    if driver.execute("document.querySelector('.a-modal-scroller.a-declarative').click()", vec![]).await.is_ok() {
        return Ok(());
    }
    sleep(Duration::from_millis(400)).await;

    if let Ok(modal) = driver.find(By::Css(".a-modal-scroller.a-declarative")).await {
        modal.click().await?;
    }

    Ok(())
}

async fn send_telegram_notification(
    rating: &Rating,
    title: &str,
    tax_price: &str,
    category: &Category,
    link: &str,
    is_free: bool,
) -> Result<()> {
    let bot = Bot::new(TELEGRAM_BOT_TOKEN);

    let price = "✅ kostenlos ✅";

    let rating_text = match (&rating.stars, &rating.total) {
        (Some(stars), Some(total)) => {
            if *total == 1 {
                format!("{} / 5.0   {} Bewertung", stars, total)
            } else {
                format!("{} / 5.0   {} Bewertungen", stars, total)
            }
        }
        _ => String::new(),
    };

    let mut parts = vec![
        format!("<b>{}</b>", price),
        format!("<b>{}</b>", title),
    ];

    if !rating_text.is_empty() {
        parts.push(rating_text);
    }

    if let Some(price) = &rating.price {
        parts.push(format!("Regulär: {}", price));
    }

    parts.push(category.root.clone());
    parts.push(link.to_string());

    let caption = parts.join("\n");

    let chat_id = match category.root.as_str() {
        "Baby" | "Spielzeug" | "Lebensmittel & Getränke" | "Drogerie & Körperpflege" => {
            ChatId(TELEGRAM_GROUP_BABY_SPIELZEUG_LEBENSMITTEL_DROGERIE)
        }
        _ if !is_free && !tax_price.is_empty() => {
            if let Ok(price_value) = tax_price.trim_start_matches('€').parse::<f64>() {
                if price_value >= 100.0 {
                    ChatId(TELEGRAM_GROUP_TEUER)
                } else {
                    ChatId(TELEGRAM_GROUP_KOSTENPFLICHTIG)
                }
            } else {
                ChatId(TELEGRAM_GROUP_KOSTENPFLICHTIG)
            }
        }
        _ if is_free => ChatId(TELEGRAM_GROUP_KOSTENLOS),
        _ => ChatId(TELEGRAM_GROUP_KOSTENPFLICHTIG),
    };

    bot.send_photo(chat_id, InputFile::url(reqwest::Url::parse(&rating.image_url)?))
        .caption(caption)
        .parse_mode(teloxide::types::ParseMode::Html)
        .await?;

    Ok(())
}

async fn run_git_commands() -> Result<()> {
    use tokio::process::Command;

    let output = Command::new("git")
        .args(&["add", "."])
        .output()
        .await?;
    println!("{}", String::from_utf8_lossy(&output.stdout));

    let output = Command::new("git")
        .args(&["commit", "-m", "Update all automatically"])
        .output()
        .await?;
    println!("--> git commit -m \"Update all automatically\"");
    println!("{}", String::from_utf8_lossy(&output.stdout));

    let output = Command::new("git")
        .args(&["push"])
        .output()
        .await?;
    println!("--> git push");
    println!("{}", String::from_utf8_lossy(&output.stdout));

    Ok(())
}
