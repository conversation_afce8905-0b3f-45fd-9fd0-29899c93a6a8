[package]
name = "amazon-vine-rust"
version = "0.1.0"
edition = "2024"

[dependencies]
# Async runtime
tokio = { version = "1.0", features = ["full"] }

# Web scraping with Selenium
thirtyfour = "0.32"

# Database
rusqlite = { version = "0.31", features = ["bundled"] }

# Telegram bot
teloxide = { version = "0.12", features = ["macros"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Date and time
chrono = { version = "0.4", features = ["serde"] }

# HTTP client for downloading images
reqwest = { version = "0.11", features = ["json"] }

# Random number generation for shuffling
rand = "0.8"

# Error handling
anyhow = "1.0"

# Regex
regex = "1.0"

# Logging
log = "0.4"
env_logger = "0.10"

# Lazy static for global variables
lazy_static = "1.4"
