import com.github.kotlintelegrambot.bot
import com.github.kotlintelegrambot.entities.ChatId
import com.github.kotlintelegrambot.entities.ParseMode
import com.github.kotlintelegrambot.entities.TelegramFile
import com.github.pgreze.process.process
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.openqa.selenium.By
import org.openqa.selenium.ElementClickInterceptedException
import org.openqa.selenium.ElementNotInteractableException
import org.openqa.selenium.NoSuchElementException
import org.openqa.selenium.WebElement
import org.openqa.selenium.chrome.ChromeDriver
import org.openqa.selenium.chrome.ChromeOptions
import java.io.File
import java.sql.Connection
import kotlin.system.exitProcess

val telegramGroupKostenpflichtig = ChatId.fromId(-1002173492657)
val telegramGroupKostenlos = ChatId.fromId(-4789277670)
val telegramGroupTeuer = ChatId.fromId(-4782393964)
val telegramGroupBabySpielzeugLebensmittelDrogerie = ChatId.fromId(-4778594099)

var insertedNewFreeArticles = 0
var totalNewBlacklistedArticles = 0
var totalNewArticles = 0

val categoriesInOrder = listOf(
    CategoryUrl("&pn=355007011", "Baby", info = "17%", totalArticles = 400),
    CategoryUrl("&pn=12950651", "Spielzeug", info = "3%", totalArticles = 2700),
    CategoryUrl("&pn=340846031", "Lebensmittel & Getränke", info = "37", totalArticles = 100),
    CategoryUrl("&pn=64187031", "Drogerie & Körperpflege", info = "36", totalArticles = 1000),
)

// https://www.amazon.de/vine/vine-items?queue=encore
@Suppress("RemoveExplicitTypeArguments")
val categoriesToShuffle = listOf<CategoryUrl>(
    CategoryUrl("&pn=84230031", "Kosmetik", info = "60%", totalArticles = 800),
    CategoryUrl("&pn=11961464031", "Fashion", info = "4", totalArticles = 4000),
    CategoryUrl("&pn=908823031", "Elektro-Großgeräte", info = "3", totalArticles = 70),
    CategoryUrl("&pn=340849031", "Musikinstrumente & DJ-Equipment", info = "2", totalArticles = 80),
    CategoryUrl("&pn=16435051", "Sport & Freizeit", info = "2", totalArticles = 1500),
    CategoryUrl("&pn=10925031", "Garten", info = "1", totalArticles = 600),
    CategoryUrl("&pn=5866098031", "Gewerbe, Industrie & Wissenschaft", info = "1", totalArticles = 300),
    CategoryUrl("&pn=300992", "Games", info = "???", totalArticles = 70),
    CategoryUrl("&pn=213083031", "Beleuchtung", info = "???", totalArticles = 400),
    CategoryUrl("&pn=72921031", "Sonstiges", info = "???", totalArticles = 400),
    CategoryUrl("&pn=78191031", "Auto & Motorrad", info = "???", totalArticles = 1100),
    CategoryUrl("&pn=908823031", "Elektro-Großgeräte ", info = "???", totalArticles = 60),
    CategoryUrl("&pn=192416031", "Bürobedarf & Schreibwaren", info = "1%", totalArticles = 700),

    // not worth it
//    CategoryUrl("&pn=562066", "Elektronik & Foto", info = "did not find anything on 10 pages, I assume close to 0% or 0%", totalArticles = 4500),
//    CategoryUrl("&pn=3167641", "Küche, Haushalt & Wohnen", info = "close to 0%", totalArticles = 4000),
//    CategoryUrl("&pn=340843031", "Computer & Zubehör", info = "0% to 1%", totalArticles = 700),
//    CategoryUrl("&pn=80084031", "Baumarkt", info = "close to 0%", totalArticles = 1200),
//    CategoryUrl("&pn=340852031", "Haustier", info = "maybe 1% - 3%", totalArticles = 500),
)

fun main() {
    /*
        runBlocking {
            val bot = bot {
                token = "7217551830:AAHOrlNv1J90nqs2ujvLcKgn5wNGbe2a-YU"
            }
            bot.sendMessage(
                telegramGroupKostenlos,
                text = "test",
                parseMode = ParseMode.HTML
            )
        }
        return
    */

/*
    runBlocking {
        println("--> git pull")
        val p = process("git", "pull")
        println(p.output.joinToString("\n"))
        if (p.resultCode != 0) {
            exitProcess(1)
        }
    }
*/
    val startTime = System.currentTimeMillis()
    Database.connect("jdbc:sqlite:data.db", "org.sqlite.JDBC")
    TransactionManager.manager.defaultIsolationLevel = Connection.TRANSACTION_SERIALIZABLE
    val login = File("login.txt").readText().trim().split("\n").filter { it.isNotBlank() }
    val options = ChromeOptions()
    options.addArguments("user-data-dir=selenium")
    val driver = ChromeDriver(options)
    amazonRatingDriver = ChromeDriver()
    val mergedCategories = categoriesInOrder.toMutableList()
    try {
        driver.get("https://www.amazon.de/vine/vine-items?queue=encore")
        login(driver, login)
        mergedCategories.addAll(categoriesToShuffle.shuffled())
        mergedCategories.forEachIndexed { index, category ->
            driver.get("https://www.amazon.de/vine/vine-items?queue=encore" + category.url)
            Thread.sleep(2000)
            checkAllArticlesOnPage(driver, index, mergedCategories.size, Category(root = category.name))
        }
        val endTime = System.currentTimeMillis()
        val dateTime = getCurrentDateTime()
        println(
            "\nAll ${mergedCategories.size} categories processed in " +
                    "~${Math.round((endTime - startTime) / 1000.0 / 60.0)} minutes. " +
                    "Inserted $insertedNewFreeArticles new free article(s). " +
                    "Blacklisted $totalNewBlacklistedArticles of $totalNewArticles new. " +
                    "Finished on $dateTime."
        )
    } catch (e: Exception) {
        println("\n\nException:\n\n$e")
    } finally {
        amazonRatingDriver?.quit()
        driver.quit()
        println("\nPushing to repository...\n\n--> git add .")
        runBlocking {
            println(process("git", "add", ".").output.joinToString("\n"))
            println("--> git commit -m \"Update all automatically\"")
            println(process("git", "commit", "-m", "Update all automatically").output.joinToString("\n"))
            println("--> git push")
            println(process("git", "push").output.joinToString("\n"))
            exitProcess(0)
        }
    }
}

fun retryWithFunctionsIgnoringExceptions(vararg functions: () -> Unit) {
    functions.forEach { function ->
        try {
            function()
        } catch (ex: Exception) {
            when (ex) {
                is ElementClickInterceptedException, is ElementNotInteractableException -> {
                    Thread.sleep(400)
                }

                else -> throw ex
            }
        }
    }
}

private fun addBlacklisted(base: WebElement, title: String, link: String, pageNumber: Int, category: Category) {
    val imageUrl = base.findElement(By.tagName("img")).getDomAttribute("src") ?: ""
    addToDatabase(
        Item(
            title = title,
            taxPrice = "",
            link = link,
            description = "",
            category = category.root,
            subCategory = category.subCategory,
            imageUrl = imageUrl,
            dateAdded = getCurrentDateTime(),
            pageNumber = pageNumber,
            fromQuickMode = 1,
            isBlacklisted = 1
        )
    )
}

private fun checkAllArticlesOnPage(driver: ChromeDriver, index: Int, categoryCount: Int, category: Category) {
    println("\n${index + 1}/${categoryCount} $category")
    var pageNumber = 0
    while (true) {
        pageNumber++
        val all = driver.findElements(By.className("vvp-item-tile"))
        // read blacklist for quicker updates each time
        val blacklist = readBlacklist()
        // I had an approach where I would exit the loop if at least one article was already inserted, but it often led to many
        // skipped articles
        var insertedAnythingInDatabase = false
        all.forEach {
            // note that the link inside the opened modal is not the same as the one below
            // but on clicking the link, it will update to the one in the modal
            var overviewLink = try {
                it.findElement(By.className("a-link-normal")).getDomAttribute("href")
            } catch (e: NoSuchElementException) {
                println("\n❌ .a-link-normal error, skipping article: $e\n")
                return@forEach
            }
            overviewLink = "https://www.amazon.de$overviewLink"
            if (isInDatabase(overviewLink, fromQuickMode = false)) {
                return@forEach
            }
            totalNewArticles++
            val overviewTitle = it.findElement(By.className("vvp-item-product-title-container")).text.trim()
//            val isBlacklisted = blacklist.any { blacklistEntry -> overviewTitle.lowercase().contains(blacklistEntry) }
            if (blacklist.any { blacklistEntry -> overviewTitle.lowercase().contains(blacklistEntry) }) {
                insertedAnythingInDatabase = true
                addBlacklisted(it, overviewTitle, overviewLink, pageNumber, category)
                totalNewBlacklistedArticles++
                return@forEach
            }
            retryWithFunctionsIgnoringExceptions(
                { it.findElement(By.className("a-button-primary")).click() },
                { driver.executeScript("document.querySelector('.a-modal-scroller.a-declarative').click()") },
                { driver.findElement(By.cssSelector(".a-modal-scroller.a-declarative")).click() },
                { driver.findElement(By.id("vvp-product-details-modal--back-btn")).click() })
            Thread.sleep(2000)
            val titleElement = driver.findElement(By.id("vvp-product-details-modal--product-title"))
            if (blacklist.any { blacklistEntry -> titleElement.text.lowercase().contains(blacklistEntry) }) {
                insertedAnythingInDatabase = true
                addBlacklisted(it, titleElement.text, overviewLink, pageNumber, category)
                return@forEach
            }
            val taxPrice = driver.findElement(By.id("vvp-product-details-modal--tax-value-string")).text
            val imageUrl = driver.findElement(By.id("vvp-product-details-modal--hero-image")).getDomAttribute("src")
            val description = driver.findElement(By.id("vvp-product-description-expander")).text
            var rating = Rating(
                imageUrl = "",
                stars = null,
                total = null,
                price = null
            )
            val isFree = taxPrice == "€0.00"
//            if (taxPrice == "€0.00" || category.root == "Spielzeug") {
            if (isFree) {
                rating = downloadAmazonRating(overviewLink)
                // TODO it can crash here, when there is an issue with the bot library - but no exception or any way to catch :(
                val bot = bot {
                    token = "7217551830:AAHOrlNv1J90nqs2ujvLcKgn5wNGbe2a-YU"
                }
                val price = "✅ kostenlos ✅"
                /*
                                val price = if (isFree) {
                                    "✅ kostenlos ✅"
                                } else if (taxPrice.isBlank()) {
                                    "???"
                                } else {
                                    taxPrice
                                }
                */
                val ratingText = if (rating.stars != null && rating.total != null) {
                    if (rating.total == 1) {
                        "${rating.stars} / 5.0   ${rating.total} Bewertung"
                    } else {
                        "${rating.stars} / 5.0   ${rating.total} Bewertungen"
                    }
                } else {
                    ""
                }
                val parts = buildList {
                    add("<b>$price</b>")
                    add("<b>$overviewTitle</b>")
                    add(ratingText)
                    if (rating.price != null) {
                        add("Regulär: " + rating.price)
                    }
                    add(category.root)
                    add(overviewLink)
                }.filter { s -> s.isNotBlank() }
                val telegramGroup = when {
                    category.root == "Baby" || category.root == "Spielzeug" || category.root == "Lebensmittel & Getränke"
                            || category.root == "Drogerie & Körperpflege" ->
                        telegramGroupBabySpielzeugLebensmittelDrogerie

                    !isFree && taxPrice.isNotBlank() && taxPrice.removePrefix("€")
                        .toDouble() >= 100.0 -> telegramGroupTeuer

                    isFree -> telegramGroupKostenlos
                    else -> telegramGroupKostenpflichtig
                }
                bot.sendPhoto(
                    chatId = telegramGroup,
                    photo = TelegramFile.ByUrl(rating.imageUrl),
                    caption = parts.joinToString("\n"),
                    parseMode = ParseMode.HTML
                )
                /*
                                bot.sendMessage(
                                    telegramGroupKostenlos,
                                    text = parts.joinToString("\n"),
                                    parseMode = ParseMode.HTML
                                )
                */
            }
            addToDatabase(
                Item(
                    title = titleElement.text,
                    taxPrice = taxPrice,
                    link = overviewLink,
                    description = description,
                    category = category.root,
                    subCategory = category.subCategory,
                    imageUrl = imageUrl ?: "",
                    dateAdded = getCurrentDateTime(),
                    pageNumber = pageNumber,
                    fromQuickMode = 0,
                    ratingStar = rating.stars,
                    ratingTotal = rating.total
                )
            )
            insertedAnythingInDatabase = true
            retryWithFunctionsIgnoringExceptions(
                { driver.findElement(By.id("vvp-product-details-modal--back-btn")).click() },
                { driver.executeScript("document.querySelector('.a-modal-scroller.a-declarative').click()") },
                { driver.findElement(By.cssSelector(".a-modal-scroller.a-declarative")).click() })
            Thread.sleep(2000)
        }
        if (!insertedAnythingInDatabase || isOnLastPage(driver, pageNumber, category)) {
            if (!insertedAnythingInDatabase) {
                println("$category: No new articles found on page $pageNumber")
                Thread.sleep(2000)
            }
            return
        }
        try {
            driver.findElement(By.className("a-last")).click()
        } catch (_: NoSuchElementException) {
            val currentUrl = driver.currentUrl ?: throw IllegalStateException("Driver currentUrl is null?")
            driver.get(currentUrl)
        }
        Thread.sleep(2000)
    }
}